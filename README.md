# OKVIP UI - React Project

Đây là project ReactJS tái tạo giao diện OKVIP với hiệu ứng rèm sân khấu và logo tương tác.

## Tính năng

- ✨ Giao diện rèm sân khấu với hiệu ứng động
- 🎭 Logo OKVIP với hiệu ứng phát sáng
- 👌 Biểu tượng tay "OK" được vẽ bằng CSS
- 🎮 Nút play tương tác
- 🔧 Thanh điều khiển bên phải
- 💫 Hiệu ứng ánh sáng và particles

## Cài đặt

### Yêu cầu
- Node.js (phiên bản 16 trở lên)
- npm hoặc yarn

### Các bước cài đặt

1. Cài đặt dependencies:
```bash
npm install
# hoặc
yarn install
```

2. Chạy development server:
```bash
npm run dev
# hoặc
yarn dev
```

3. Mở trình duyệt và truy cập: `http://localhost:5173`

## Cấu trúc project

```
src/
├── components/
│   ├── Header.jsx          # Header với menu điều hướng
│   ├── Header.css
│   ├── MainContent.jsx     # Logo chính và biểu tượng tay
│   ├── MainContent.css
│   ├── SideControls.jsx    # Thanh điều khiển bên phải
│   ├── SideControls.css
│   ├── CurtainBackground.jsx # Background rèm sân khấu
│   └── CurtainBackground.css
├── App.jsx                 # Component chính
├── App.css
├── main.jsx               # Entry point
└── index.css              # Global styles
```

## Scripts

- `npm run dev` - Chạy development server
- `npm run build` - Build production
- `npm run preview` - Preview production build
- `npm run lint` - Chạy ESLint

## Công nghệ sử dụng

- **React 18** - UI framework
- **Vite** - Build tool và dev server
- **CSS3** - Animations và effects
- **ESLint** - Code linting

## Tính năng nổi bật

### 1. Hiệu ứng rèm sân khấu
- Gradient màu đỏ/vàng động
- Animation wave cho các dải rèm
- Hiệu ứng spotlight

### 2. Logo tương tác
- Biểu tượng tay "OK" được vẽ hoàn toàn bằng CSS
- Hiệu ứng pulse và glow
- Nút play với hover effect

### 3. Thanh điều khiển
- Icons được vẽ bằng CSS
- Hover effects mượt mà
- Backdrop blur effect

### 4. Responsive design
- Tối ưu cho các kích thước màn hình khác nhau
- Flexible layout

## Tùy chỉnh

Bạn có thể tùy chỉnh:
- Màu sắc trong các file CSS
- Tốc độ animation
- Kích thước components
- Thêm các hiệu ứng mới

## Hỗ trợ

Nếu gặp vấn đề, hãy kiểm tra:
1. Node.js đã được cài đặt chưa
2. Dependencies đã được cài đặt chưa
3. Port 5173 có bị chiếm dụng không

## License

MIT License
