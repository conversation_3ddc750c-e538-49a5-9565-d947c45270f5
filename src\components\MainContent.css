.main-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

/* Hand Gesture */
.hand-gesture {
  position: relative;
  z-index: 2;
}

.hand {
  position: relative;
  width: 120px;
  height: 120px;
}

.thumb {
  position: absolute;
  top: 30px;
  left: 20px;
  width: 25px;
  height: 60px;
  background: #FFD700;
  border-radius: 15px 15px 8px 8px;
  transform: rotate(-20deg);
  box-shadow: 
    inset 2px 2px 5px rgba(255, 255, 255, 0.3),
    inset -2px -2px 5px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(255, 215, 0, 0.6);
}

.fingers {
  position: absolute;
  top: 10px;
  left: 45px;
}

.finger {
  position: absolute;
  background: #FFD700;
  border-radius: 8px;
  box-shadow: 
    inset 2px 2px 5px rgba(255, 255, 255, 0.3),
    inset -2px -2px 5px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(255, 215, 0, 0.4);
}

.finger-1 {
  width: 18px;
  height: 50px;
  left: 0;
  transform: rotate(-10deg);
}

.finger-2 {
  width: 18px;
  height: 55px;
  left: 20px;
  top: -5px;
}

.finger-3 {
  width: 18px;
  height: 50px;
  left: 40px;
  transform: rotate(10deg);
}

/* OK Logo */
.ok-logo {
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
}

.ok-background {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FFD700 100%);
  border-radius: 20px;
  padding: 30px 50px;
  position: relative;
  box-shadow: 
    0 0 30px rgba(255, 215, 0, 0.8),
    inset 0 0 20px rgba(255, 255, 255, 0.2),
    0 10px 30px rgba(0, 0, 0, 0.3);
  animation: pulse 3s ease-in-out infinite;
}

.ok-text {
  font-size: 72px;
  font-weight: bold;
  color: #000;
  text-shadow: 
    2px 2px 0px #FFF,
    4px 4px 10px rgba(0, 0, 0, 0.3);
  font-family: 'Arial Black', sans-serif;
}

.play-button {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #32CD32 0%, #228B22 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 0 20px rgba(50, 205, 50, 0.6),
    inset 0 0 10px rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.play-triangle {
  width: 0;
  height: 0;
  border-left: 20px solid white;
  border-top: 12px solid transparent;
  border-bottom: 12px solid transparent;
  margin-left: 4px;
}

/* Glow Effect */
.glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow 4s ease-in-out infinite;
  z-index: -1;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 0 30px rgba(255, 215, 0, 0.8),
      inset 0 0 20px rgba(255, 255, 255, 0.2),
      0 10px 30px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow:
      0 0 50px rgba(255, 215, 0, 1),
      inset 0 0 30px rgba(255, 255, 255, 0.3),
      0 15px 40px rgba(0, 0, 0, 0.4);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1.2);
  }
}
