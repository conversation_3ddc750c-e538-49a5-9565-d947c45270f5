import React from 'react'
import './Header.css'

const Header = () => {
  const menuItems = [
    'Trang chủ',
    'Thành viên liên minh',
    'Game show',
    'Tài trợ',
    '<PERSON> tức',
    '<PERSON><PERSON><PERSON> hệ',
    '<PERSON><PERSON><PERSON> hồi',
    '<PERSON><PERSON><PERSON> ký'
  ]

  return (
    <header className="header">
      <div className="header-container">
        <div className="logo">
          <span className="logo-text">OKVIP</span>
        </div>
        
        <nav className="navigation">
          {menuItems.map((item, index) => (
            <a key={index} href="#" className="nav-item">
              {item}
            </a>
          ))}
        </nav>

        <div className="user-section">
          <div className="user-info">
            <span className="user-name">Xin chào</span>
            <span className="user-greeting">Nguyễn Văn A</span>
          </div>
          <div className="user-avatar">
            <img src="/api/placeholder/32/32" alt="User Avatar" />
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
