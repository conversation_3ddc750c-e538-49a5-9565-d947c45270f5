import React from 'react'
import './SideControls.css'

const SideControls = () => {
  return (
    <div className="side-controls">
      <div className="control-button volume-control">
        <div className="control-icon">
          <div className="speaker-icon">
            <div className="speaker-base"></div>
            <div className="sound-waves">
              <div className="wave wave-1"></div>
              <div className="wave wave-2"></div>
              <div className="wave wave-3"></div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="control-button settings-control">
        <div className="control-icon">
          <div className="gear-icon">
            <div className="gear-center"></div>
            <div className="gear-teeth">
              {Array.from({ length: 8 }, (_, i) => (
                <div key={i} className="gear-tooth" style={{
                  transform: `rotate(${i * 45}deg) translateY(-12px)`
                }}></div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      <div className="control-button fullscreen-control">
        <div className="control-icon">
          <div className="fullscreen-icon">
            <div className="corner corner-tl"></div>
            <div className="corner corner-tr"></div>
            <div className="corner corner-bl"></div>
            <div className="corner corner-br"></div>
          </div>
        </div>
      </div>
      
      <div className="control-button help-control">
        <div className="control-icon">
          <div className="question-icon">
            <div className="question-mark">?</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SideControls
