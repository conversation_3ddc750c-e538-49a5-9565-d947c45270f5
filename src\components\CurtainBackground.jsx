import React from 'react'
import './CurtainBackground.css'

const CurtainBackground = () => {
  return (
    <div className="curtain-background">
      <div className="curtain-container">
        {/* Red curtain stripes */}
        {Array.from({ length: 20 }, (_, i) => (
          <div 
            key={i} 
            className="curtain-stripe" 
            style={{ 
              left: `${i * 5}%`,
              animationDelay: `${i * 0.1}s`
            }}
          />
        ))}
      </div>
      
      {/* Spotlight effects */}
      <div className="spotlight spotlight-1" />
      <div className="spotlight spotlight-2" />
      <div className="spotlight spotlight-3" />
      
      {/* Golden particles */}
      <div className="particles">
        {Array.from({ length: 15 }, (_, i) => (
          <div 
            key={i} 
            className="particle" 
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>
    </div>
  )
}

export default CurtainBackground
