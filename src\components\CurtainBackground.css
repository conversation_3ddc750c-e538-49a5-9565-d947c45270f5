.curtain-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #8B0000 0%, #DC143C 25%, #B22222 50%, #8B0000 75%, #DC143C 100%);
  z-index: -1;
}

.curtain-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.curtain-stripe {
  position: absolute;
  top: 0;
  width: 5%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(139, 0, 0, 0.9) 0%,
    rgba(220, 20, 60, 0.8) 25%,
    rgba(178, 34, 34, 0.9) 50%,
    rgba(139, 0, 0, 0.8) 75%,
    rgba(220, 20, 60, 0.9) 100%
  );
  animation: curtainWave 4s ease-in-out infinite;
  box-shadow: inset 2px 0 10px rgba(0, 0, 0, 0.3);
}

.curtain-stripe:nth-child(even) {
  background: linear-gradient(
    to bottom,
    rgba(178, 34, 34, 0.9) 0%,
    rgba(139, 0, 0, 0.8) 25%,
    rgba(220, 20, 60, 0.9) 50%,
    rgba(178, 34, 34, 0.8) 75%,
    rgba(139, 0, 0, 0.9) 100%
  );
  animation-direction: reverse;
}

@keyframes curtainWave {
  0%, 100% { 
    transform: scaleY(1) skewX(0deg);
    filter: brightness(1);
  }
  25% { 
    transform: scaleY(1.02) skewX(0.5deg);
    filter: brightness(1.1);
  }
  50% { 
    transform: scaleY(1.01) skewX(-0.5deg);
    filter: brightness(0.9);
  }
  75% { 
    transform: scaleY(1.03) skewX(0.3deg);
    filter: brightness(1.05);
  }
}

.spotlight {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
  animation: spotlight 6s ease-in-out infinite;
}

.spotlight-1 {
  top: 10%;
  left: 20%;
  width: 300px;
  height: 300px;
  animation-delay: 0s;
}

.spotlight-2 {
  top: 30%;
  right: 15%;
  width: 250px;
  height: 250px;
  animation-delay: 2s;
}

.spotlight-3 {
  bottom: 20%;
  left: 10%;
  width: 200px;
  height: 200px;
  animation-delay: 4s;
}

@keyframes spotlight {
  0%, 100% { 
    opacity: 0.2;
    transform: scale(1);
  }
  50% { 
    opacity: 0.6;
    transform: scale(1.2);
  }
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #FFD700;
  border-radius: 50%;
  animation: float 5s ease-in-out infinite;
  box-shadow: 0 0 10px #FFD700;
}

@keyframes float {
  0%, 100% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-10px) rotate(360deg);
    opacity: 0;
  }
}
