.side-controls {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 1000;
}

.control-button {
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.control-button:hover {
  background: rgba(255, 215, 0, 0.2);
  border-color: #FFD700;
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.control-icon {
  width: 24px;
  height: 24px;
  position: relative;
  color: #FFD700;
}

/* Speaker Icon */
.speaker-icon {
  position: relative;
  width: 100%;
  height: 100%;
}

.speaker-base {
  width: 8px;
  height: 12px;
  background: #FFD700;
  position: absolute;
  left: 2px;
  top: 6px;
}

.speaker-base::before {
  content: '';
  position: absolute;
  left: -4px;
  top: 2px;
  width: 0;
  height: 0;
  border-right: 8px solid #FFD700;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.sound-waves {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.wave {
  position: absolute;
  border: 2px solid #FFD700;
  border-left: none;
  border-radius: 0 50px 50px 0;
  animation: soundWave 2s ease-in-out infinite;
}

.wave-1 {
  width: 6px;
  height: 6px;
  right: 0;
  top: -3px;
}

.wave-2 {
  width: 8px;
  height: 10px;
  right: 2px;
  top: -5px;
  animation-delay: 0.3s;
}

.wave-3 {
  width: 10px;
  height: 14px;
  right: 4px;
  top: -7px;
  animation-delay: 0.6s;
}

@keyframes soundWave {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Gear Icon */
.gear-icon {
  position: relative;
  width: 100%;
  height: 100%;
}

.gear-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #FFD700;
  border-radius: 50%;
}

.gear-teeth {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: rotate 4s linear infinite;
}

.gear-tooth {
  position: absolute;
  width: 3px;
  height: 6px;
  background: #FFD700;
  border-radius: 1px;
}

@keyframes rotate {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Fullscreen Icon */
.fullscreen-icon {
  position: relative;
  width: 100%;
  height: 100%;
}

.corner {
  position: absolute;
  width: 6px;
  height: 6px;
  border: 2px solid #FFD700;
}

.corner-tl {
  top: 2px;
  left: 2px;
  border-right: none;
  border-bottom: none;
}

.corner-tr {
  top: 2px;
  right: 2px;
  border-left: none;
  border-bottom: none;
}

.corner-bl {
  bottom: 2px;
  left: 2px;
  border-right: none;
  border-top: none;
}

.corner-br {
  bottom: 2px;
  right: 2px;
  border-left: none;
  border-top: none;
}

/* Question Icon */
.question-icon {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.question-mark {
  font-size: 18px;
  font-weight: bold;
  color: #FFD700;
  font-family: Arial, sans-serif;
}
